---
import MainLayout from '../layouts/MainLayout.astro';
---

<MainLayout title="常見問題 - 青釭金融科技">
	<!-- Hero Section -->
	<section class="bg-gradient-to-r from-orange-600 to-red-600 text-white py-16">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center">
				<h1 class="text-4xl md:text-5xl font-bold mb-4">
					常見問題
				</h1>
				<p class="text-xl text-orange-100">
					解答客戶和投資者的常見問題
				</p>
			</div>
		</div>
	</section>

	<!-- Search & Categories -->
	<section class="py-16 bg-gray-50">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-12">
				<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
					快速找到答案
				</h2>
				<div class="max-w-md mx-auto mb-8">
					<div class="relative">
						<input
							type="text"
							placeholder="搜索問題..."
							class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
						/>
						<svg class="absolute left-4 top-3.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
						</svg>
					</div>
				</div>
			</div>
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				<div class="bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow cursor-pointer">
					<div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
						</svg>
					</div>
					<h3 class="text-lg font-semibold">產品相關</h3>
					<p class="text-sm text-gray-600 mt-2">8個問題</p>
				</div>

				<div class="bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow cursor-pointer">
					<div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
						</svg>
					</div>
					<h3 class="text-lg font-semibold">技術安全</h3>
					<p class="text-sm text-gray-600 mt-2">6個問題</p>
				</div>

				<div class="bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow cursor-pointer">
					<div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
						</svg>
					</div>
					<h3 class="text-lg font-semibold">價格方案</h3>
					<p class="text-sm text-gray-600 mt-2">5個問題</p>
				</div>

				<div class="bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow cursor-pointer">
					<div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
						</svg>
					</div>
					<h3 class="text-lg font-semibold">服務支持</h3>
					<p class="text-sm text-gray-600 mt-2">7個問題</p>
				</div>
			</div>
		</div>
	</section>

	<!-- FAQ Content -->
	<section class="py-16">
		<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
			<h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">
				產品相關問題
			</h2>
			<div class="space-y-6">
				<div class="bg-white border border-gray-200 rounded-lg">
					<button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500">
						<span class="font-semibold text-gray-900">FL.AI隱私計算平台適用於哪些場景？</span>
						<svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
						</svg>
					</button>
					<div class="px-6 pb-4">
						<p class="text-gray-600">
							FL.AI隱私計算平台主要適用於需要多方數據協作但不能直接共享數據的場景，包括：
							<br>• 金融風控：銀行間聯合建模，提升風險識別能力
							<br>• 醫療健康：醫院間協作診斷，保護患者隱私
							<br>• 政府服務：跨部門數據協作，提升公共服務效率
							<br>• 廣告投放：廣告主與媒體方聯合建模，保護用戶隱私
						</p>
					</div>
				</div>

				<div class="bg-white border border-gray-200 rounded-lg">
					<button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500">
						<span class="font-semibold text-gray-900">如何評估MXC.AI的數據處理能力？</span>
						<svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
						</svg>
					</button>
					<div class="px-6 pb-4 hidden">
						<p class="text-gray-600">
							MXC.AI數據智能處理器的處理能力可以從以下幾個方面評估：
							<br>• 數據吞吐量：每秒可處理百萬級數據記錄
							<br>• 實時性能：毫秒級響應時間，支持實時數據分析
							<br>• 擴展性：支持水平擴展，可根據業務需求調整處理能力
							<br>• 準確性：99.9%的數據處理準確率
							<br>我們提供免費的性能評估服務，可以根據您的實際數據進行測試。
						</p>
					</div>
				</div>

				<div class="bg-white border border-gray-200 rounded-lg">
					<button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500">
						<span class="font-semibold text-gray-900">Model.AI支持哪些機器學習算法？</span>
						<svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
						</svg>
					</button>
					<div class="px-6 pb-4 hidden">
						<p class="text-gray-600">
							Model.AI支持廣泛的機器學習算法，包括：
							<br>• 監督學習：決策樹、隨機森林、支持向量機、神經網絡
							<br>• 無監督學習：K-means聚類、主成分分析、異常檢測
							<br>• 深度學習：CNN、RNN、LSTM、Transformer
							<br>• 強化學習：Q-learning、Actor-Critic
							<br>• 自然語言處理：BERT、GPT等預訓練模型
							<br>同時支持自定義算法和模型導入。
						</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Technical Security FAQ -->
	<section class="py-16 bg-gray-50">
		<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
			<h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">
				技術安全問題
			</h2>
			<div class="space-y-6">
				<div class="bg-white border border-gray-200 rounded-lg">
					<button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500">
						<span class="font-semibold text-gray-900">隱私計算如何保證數據安全？</span>
						<svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
						</svg>
					</button>
					<div class="px-6 pb-4">
						<p class="text-gray-600">
							我們採用多層次的安全保護機制：
							<br>• 聯邦學習：數據不出域，只交換模型參數
							<br>• 差分隱私：添加數學噪聲，保護個體隱私
							<br>• 同態加密：在加密狀態下進行計算
							<br>• 安全多方計算：多方協作計算，無需信任第三方
							<br>• 數據脫敏：敏感數據去標識化處理
							<br>所有技術都經過嚴格的安全審計和合規認證。
						</p>
					</div>
				</div>

				<div class="bg-white border border-gray-200 rounded-lg">
					<button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500">
						<span class="font-semibold text-gray-900">系統是否符合數據保護法規？</span>
						<svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
						</svg>
					</button>
					<div class="px-6 pb-4 hidden">
						<p class="text-gray-600">
							是的，我們的系統完全符合國際和地區的數據保護法規：
							<br>• GDPR（歐盟一般數據保護條例）
							<br>• CCPA（加州消費者隱私法案）
							<br>• 中國個人信息保護法
							<br>• 台灣個人資料保護法
							<br>我們定期進行合規審計，確保系統始終符合最新的法規要求。
						</p>
					</div>
				</div>

				<div class="bg-white border border-gray-200 rounded-lg">
					<button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500">
						<span class="font-semibold text-gray-900">如何防範AI模型攻擊？</span>
						<svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
						</svg>
					</button>
					<div class="px-6 pb-4 hidden">
						<p class="text-gray-600">
							我們實施多種防護措施來對抗AI模型攻擊：
							<br>• 對抗訓練：增強模型的魯棒性
							<br>• 輸入檢測：識別異常和惡意輸入
							<br>• 模型加密：保護模型參數不被竊取
							<br>• 聯邦學習：分散式訓練降低單點攻擊風險
							<br>• 持續監控：實時檢測異常行為
							<br>• 定期更新：及時修復安全漏洞
						</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Pricing FAQ -->
	<section class="py-16">
		<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
			<h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">
				價格方案問題
			</h2>
			<div class="space-y-6">
				<div class="bg-white border border-gray-200 rounded-lg">
					<button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500">
						<span class="font-semibold text-gray-900">有哪些計費模式可以選擇？</span>
						<svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
						</svg>
					</button>
					<div class="px-6 pb-4">
						<p class="text-gray-600">
							我們提供多種靈活的計費模式：
							<br>• SaaS訂閱：按月/年訂閱，適合中小企業
							<br>• 按量付費：根據實際使用量計費，適合輕量級使用
							<br>• 私有化部署：一次性購買，適合大企業
							<br>• 混合模式：結合多種計費方式，靈活配置
							<br>• 定制方案：根據企業需求量身定制
							<br>我們會根據您的具體需求推薦最適合的計費方案。
						</p>
					</div>
				</div>

				<div class="bg-white border border-gray-200 rounded-lg">
					<button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500">
						<span class="font-semibold text-gray-900">是否提供免費試用？</span>
						<svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
						</svg>
					</button>
					<div class="px-6 pb-4 hidden">
						<p class="text-gray-600">
							是的，我們為所有產品提供免費試用：
							<br>• 標準版：30天免費試用
							<br>• 企業版：14天免費試用
							<br>• 定制版：提供POC（概念驗證）服務
							<br>• 技術支持：試用期間提供完整技術支持
							<br>• 數據遷移：協助現有數據的遷移和集成
							<br>無需信用卡，即可開始免費試用。
						</p>
					</div>
				</div>

				<div class="bg-white border border-gray-200 rounded-lg">
					<button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500">
						<span class="font-semibold text-gray-900">如何計算實際使用成本？</span>
						<svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
						</svg>
					</button>
					<div class="px-6 pb-4 hidden">
						<p class="text-gray-600">
							使用成本主要包括以下因素：
							<br>• 數據處理量：按處理的數據量計費
							<br>• 計算資源：CPU、GPU、記憶體使用量
							<br>• 儲存空間：數據和模型的儲存成本
							<br>• 網絡帶寬：數據傳輸的帶寬成本
							<br>• 服務等級：不同的SLA等級對應不同價格
							<br>我們提供成本計算器，幫助您預估使用成本。
						</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Service Support FAQ -->
	<section class="py-16 bg-gray-50">
		<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
			<h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">
				服務支持問題
			</h2>
			<div class="space-y-6">
				<div class="bg-white border border-gray-200 rounded-lg">
					<button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500">
						<span class="font-semibold text-gray-900">技術支持的響應時間是多久？</span>
						<svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
						</svg>
					</button>
					<div class="px-6 pb-4">
						<p class="text-gray-600">
							我們提供分級的技術支持服務：
							<br>• 緊急問題：1小時內響應
							<br>• 重要問題：4小時內響應
							<br>• 一般問題：24小時內響應
							<br>• 普通諮詢：48小時內響應
							<br>• 24/7支持：企業版客戶享受全天候支持
							<br>• 專屬客服：大客戶配備專屬技術支持團隊
						</p>
					</div>
				</div>

				<div class="bg-white border border-gray-200 rounded-lg">
					<button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500">
						<span class="font-semibold text-gray-900">是否提供實施和培訓服務？</span>
						<svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
						</svg>
					</button>
					<div class="px-6 pb-4 hidden">
						<p class="text-gray-600">
							是的，我們提供全方位的實施和培訓服務：
							<br>• 系統部署：專業團隊協助系統部署和配置
							<br>• 數據遷移：協助現有數據的遷移和整合
							<br>• 培訓課程：提供操作培訓和技術培訓
							<br>• 最佳實踐：分享行業最佳實踐和使用案例
							<br>• 持續優化：定期評估和優化系統性能
							<br>• 認證計劃：提供技術認證和專業認證
						</p>
					</div>
				</div>

				<div class="bg-white border border-gray-200 rounded-lg">
					<button class="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500">
						<span class="font-semibold text-gray-900">如何獲得技術文檔和資源？</span>
						<svg class="w-5 h-5 text-gray-500 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
						</svg>
					</button>
					<div class="px-6 pb-4 hidden">
						<p class="text-gray-600">
							我們提供豐富的技術文檔和學習資源：
							<br>• 在線文檔：詳細的API文檔和使用指南
							<br>• 視頻教程：分步驟的操作教學視頻
							<br>• 示例代碼：豐富的代碼示例和模板
							<br>• 開發者論壇：技術交流和問題討論
							<br>• 知識庫：常見問題和解決方案
							<br>• 定期webinar：技術分享和產品更新
						</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Contact Support -->
	<section class="py-16 bg-orange-600 text-white">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
			<h2 class="text-3xl md:text-4xl font-bold mb-4">
				沒有找到您要的答案？
			</h2>
			<p class="text-xl mb-8 text-orange-100">
				我們的專業團隊隨時為您解答疑問
			</p>
			<div class="flex flex-col sm:flex-row gap-4 justify-center">
				<a href="/contact" class="bg-white text-orange-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
					聯繫我們
				</a>
				<a href="mailto:<EMAIL>" class="bg-transparent border-2 border-white text-white hover:bg-white hover:text-orange-600 px-8 py-3 rounded-lg font-semibold transition-colors">
					發送郵件
				</a>
			</div>
		</div>
	</section>
</MainLayout>

<script>
	document.addEventListener('DOMContentLoaded', function() {
		const faqButtons = document.querySelectorAll('button[class*="hover:bg-gray-50"]');
		
		faqButtons.forEach(button => {
			button.addEventListener('click', function() {
				const content = this.nextElementSibling;
				const icon = this.querySelector('svg');
				
				if (content.classList.contains('hidden')) {
					content.classList.remove('hidden');
					icon.classList.add('rotate-180');
				} else {
					content.classList.add('hidden');
					icon.classList.remove('rotate-180');
				}
			});
		});
	});
</script> 