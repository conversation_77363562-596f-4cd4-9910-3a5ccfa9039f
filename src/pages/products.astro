---
import MainLayout from '../layouts/MainLayout.astro';
---

<MainLayout title="產品與技術 - 青釭金融科技">
	<!-- Hero Section -->
	<section class="bg-gradient-to-r from-gray-900 to-slate-900 text-white py-24">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center">
				<h1 class="text-4xl md:text-6xl font-bold mb-8">
					技術產品框架
				</h1>
				<p class="text-xl md:text-2xl mb-6 text-gray-200">
					AI+大數據+分布式加密計算
				</p>
				<p class="text-lg text-gray-300 max-w-4xl mx-auto leading-relaxed">
					以覆蓋全國C端用戶數據網絡為基礎，為B2C行業用戶搭建客戶精準運營和自動化服務體系，整合AI+智能決策、AI+智能生成等前沿技術，推動行業用戶實現營銷運營ROI提升和業務數字化轉型
				</p>
			</div>
		</div>
	</section>

	<!-- Framework Overview -->
	<section class="py-20 bg-gray-50">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-16">
				<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
					框架總覽
				</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					三層架構的AI技術產品體系，從底層基礎設施到上層應用的完整解決方案
				</p>
			</div>
			
			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-10">
				<div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
					<!-- Core Technologies -->
					<div class="lg:col-span-1">
						<h3 class="text-2xl font-bold text-gray-900 mb-8">核心技術驅動</h3>
						<div class="space-y-6">
							<div class="flex items-center">
								<div class="w-4 h-4 bg-blue-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">AI+大數據</span>
							</div>
							<div class="flex items-center">
								<div class="w-4 h-4 bg-blue-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">分布式加密計算</span>
							</div>
							<div class="flex items-center">
								<div class="w-4 h-4 bg-blue-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">覆蓋全國C端用戶數據網絡</span>
							</div>
						</div>
					</div>
					
					<!-- Target Users -->
					<div class="lg:col-span-1">
						<h3 class="text-2xl font-bold text-gray-900 mb-8">目標用戶</h3>
						<div class="space-y-6">
							<div class="flex items-center">
								<div class="w-4 h-4 bg-green-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">B2C行業用戶</span>
							</div>
							<div class="flex items-center">
								<div class="w-4 h-4 bg-green-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">客戶精準運營</span>
							</div>
							<div class="flex items-center">
								<div class="w-4 h-4 bg-green-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">自動化服務體系</span>
							</div>
						</div>
					</div>
					
					<!-- Core Services -->
					<div class="lg:col-span-1">
						<h3 class="text-2xl font-bold text-gray-900 mb-8">核心服務</h3>
						<div class="space-y-6">
							<div class="flex items-center">
								<div class="w-4 h-4 bg-purple-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">AI+智能決策</span>
							</div>
							<div class="flex items-center">
								<div class="w-4 h-4 bg-purple-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">AI+智能生成</span>
							</div>
							<div class="flex items-center">
								<div class="w-4 h-4 bg-purple-600 rounded-full mr-6"></div>
								<span class="text-gray-700 text-lg">營銷運營ROI提升</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Three Layer Architecture -->
	<section class="py-20 bg-white">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-16">
				<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
					三層架構體系
				</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					從基礎設施到應用層的完整技術堆疊
				</p>
			</div>

			<div class="bg-gray-50 rounded-lg border border-gray-200">
				<!-- Application Layer -->
				<div class="border-b border-gray-200">
					<div class="p-10">
						<div class="flex items-center mb-10">
							<div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-6">
								<span class="text-white font-bold text-lg">1</span>
							</div>
							<div>
								<h3 class="text-2xl font-bold text-gray-900 mb-2">應用層：四大AI能力</h3>
								<p class="text-gray-600 text-lg">直接面向業務應用的AI產品和服務</p>
							</div>
						</div>
						
						<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
							<!-- Model.AI -->
							<div id="model-ai" class="bg-white p-8 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
								<div class="w-14 h-14 bg-blue-50 rounded-lg flex items-center justify-center mb-6">
									<svg class="w-7 h-7 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
									</svg>
								</div>
								<h4 class="text-xl font-semibold text-gray-900 mb-3">Model.AI</h4>
								<p class="text-gray-600 mb-4">用戶畫像模型</p>
								<ul class="text-sm text-gray-500 space-y-2">
									<li>• 客群特征分析</li>
									<li>• 風險評估</li>
									<li>• 客戶行為偏好預測</li>
								</ul>
							</div>

							<!-- Strategy.AI -->
							<div id="strategy-ai" class="bg-white p-8 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
								<div class="w-14 h-14 bg-green-50 rounded-lg flex items-center justify-center mb-6">
									<svg class="w-7 h-7 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
									</svg>
								</div>
								<h4 class="text-xl font-semibold text-gray-900 mb-3">Strategy.AI</h4>
								<p class="text-gray-600 mb-4">智能決策工具</p>
								<ul class="text-sm text-gray-500 space-y-2">
									<li>• 推薦策略</li>
									<li>• 運營分析</li>
									<li>• 運營RPA工具</li>
								</ul>
							</div>

							<!-- Robot.AI -->
							<div id="robot-ai" class="bg-white p-8 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
								<div class="w-14 h-14 bg-purple-50 rounded-lg flex items-center justify-center mb-6">
									<svg class="w-7 h-7 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
									</svg>
								</div>
								<h4 class="text-xl font-semibold text-gray-900 mb-3">Robot.AI</h4>
								<p class="text-gray-600 mb-4">聊天機器人</p>
								<ul class="text-sm text-gray-500 space-y-2">
									<li>• 文案機器人</li>
									<li>• 招聘機器人</li>
									<li>• 行業場景大模型</li>
								</ul>
							</div>

							<!-- AR.Digital -->
							<div id="ar-digital" class="bg-white p-8 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
								<div class="w-14 h-14 bg-orange-50 rounded-lg flex items-center justify-center mb-6">
									<svg class="w-7 h-7 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
									</svg>
								</div>
								<h4 class="text-xl font-semibold text-gray-900 mb-3">AR.Digital</h4>
								<p class="text-gray-600 mb-4">智能虛擬人</p>
								<ul class="text-sm text-gray-500 space-y-2">
									<li>• 數字人客服</li>
									<li>• 交互機器人</li>
									<li>• AR智能虛擬人技術</li>
								</ul>
							</div>
						</div>
					</div>
				</div>

				<!-- Platform Layer -->
				<div class="border-b border-gray-200">
					<div class="p-10">
						<div class="flex items-center mb-10">
							<div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mr-6">
								<span class="text-white font-bold text-lg">2</span>
							</div>
							<div>
								<h3 class="text-2xl font-bold text-gray-900 mb-2">平台層：三大核心AI平台</h3>
								<p class="text-gray-600 text-lg">提供核心AI模型訓練、算法和數據能力</p>
							</div>
						</div>
						
						<div class="grid grid-cols-1 md:grid-cols-3 gap-8">
							<!-- MXC.AI -->
							<div id="mxc-ai" class="bg-white p-8 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
								<div class="w-16 h-16 bg-green-50 rounded-lg flex items-center justify-center mb-8">
									<svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
									</svg>
								</div>
								<h4 class="text-2xl font-semibold text-gray-900 mb-4">MXC.AI</h4>
								<p class="text-gray-600 mb-6">算法遷移訓練及模型能力開放平台</p>
								<div class="space-y-3 text-sm text-gray-500">
									<p><strong class="text-gray-700">功能：</strong>API管理、畫像標籤、算法模型庫</p>
									<p><strong class="text-gray-700">核心引擎：</strong>MXC-算法遷移學習引擎</p>
									<p><strong class="text-gray-700">支撐：</strong>智能決策和智能機器人</p>
								</div>
							</div>

							<!-- Rochat.AI -->
							<div id="rochat-ai" class="bg-white p-8 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
								<div class="w-16 h-16 bg-purple-50 rounded-lg flex items-center justify-center mb-8">
									<svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
									</svg>
								</div>
								<h4 class="text-2xl font-semibold text-gray-900 mb-4">Rochat.AI</h4>
								<p class="text-gray-600 mb-6">AI行業垂直類大模型訓練引擎</p>
								<div class="space-y-3 text-sm text-gray-500">
									<p><strong class="text-gray-700">功能：</strong>場景知識庫、NLP、GPT技術</p>
									<p><strong class="text-gray-700">核心引擎：</strong>LLM-大模型語言模型自訓練引擎</p>
									<p><strong class="text-gray-700">支撐：</strong>行業大模型訓練</p>
								</div>
							</div>

							<!-- FL.AI -->
							<div id="fl-ai" class="bg-white p-8 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
								<div class="w-16 h-16 bg-blue-50 rounded-lg flex items-center justify-center mb-8">
									<svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
									</svg>
								</div>
								<h4 class="text-2xl font-semibold text-gray-900 mb-4">FL.AI</h4>
								<p class="text-gray-600 mb-6">基於隱私加密計算技術的AI算法訓練平台</p>
								<div class="space-y-3 text-sm text-gray-500">
									<p><strong class="text-gray-700">功能：</strong>隱私請求、匿蹤查詢、聯邦學習、MPC</p>
									<p><strong class="text-gray-700">核心：</strong>數據隱私合規處理、開放及運營</p>
									<p><strong class="text-gray-700">支撐：</strong>模型訓練和算法遷移</p>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Infrastructure Layer -->
				<div>
					<div class="p-10">
						<div class="flex items-center mb-10">
							<div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mr-6">
								<span class="text-white font-bold text-lg">3</span>
							</div>
							<div>
								<h3 class="text-2xl font-bold text-gray-900 mb-2">基礎設施層：兩大核心引擎</h3>
								<p class="text-gray-600 text-lg">整個框架的技術基石</p>
							</div>
						</div>
						
						<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
							<!-- Model Training -->
							<div class="bg-white p-8 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
								<div class="w-16 h-16 bg-purple-50 rounded-lg flex items-center justify-center mb-8">
									<svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
									</svg>
								</div>
								<h4 class="text-2xl font-semibold text-gray-900 mb-4">模型訓練和算法遷移</h4>
								<p class="text-gray-600 mb-6">構建各類行業模型預測和算法訓練引擎</p>
								<div class="space-y-3 text-sm text-gray-500">
									<p><strong class="text-gray-700">支撐平台：</strong>MXC.AI 和 FL.AI</p>
									<p><strong class="text-gray-700">核心功能：</strong>模型和算法的開發基礎</p>
									<p><strong class="text-gray-700">技術特色：</strong>行業模型預測、算法訓練引擎</p>
								</div>
							</div>

							<!-- Data Encryption -->
							<div class="bg-white p-8 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
								<div class="w-16 h-16 bg-indigo-50 rounded-lg flex items-center justify-center mb-8">
									<svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
									</svg>
								</div>
								<h4 class="text-2xl font-semibold text-gray-900 mb-4">數據加密計算和流通</h4>
								<p class="text-gray-600 mb-6">基於FL分布式加密算法網絡生態</p>
								<div class="space-y-3 text-sm text-gray-500">
									<p><strong class="text-gray-700">支撐平台：</strong>FL.AI</p>
									<p><strong class="text-gray-700">核心功能：</strong>數據處理和流通中的隱私與安全</p>
									<p><strong class="text-gray-700">技術特色：</strong>聯邦學習、分布式加密算法</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Technology Flow -->
	<section class="py-20 bg-gray-50">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-16">
				<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
					技術流程與關係
				</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					從底層到上層的數據流和技術流
				</p>
			</div>

			<div class="bg-white rounded-lg shadow-sm border border-gray-200 p-12">
				<div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
					<!-- Data Flow -->
					<div class="text-center">
						<div class="w-20 h-20 bg-blue-50 rounded-lg flex items-center justify-center mx-auto mb-6">
							<svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
							</svg>
						</div>
						<h3 class="text-xl font-semibold mb-4 text-gray-900">數據流向</h3>
						<p class="text-gray-600">數據和算法能力由底層向上層流動和支撐</p>
					</div>

					<!-- Technology Integration -->
					<div class="text-center">
						<div class="w-20 h-20 bg-green-50 rounded-lg flex items-center justify-center mx-auto mb-6">
							<svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
							</svg>
						</div>
						<h3 class="text-xl font-semibold mb-4 text-gray-900">技術整合</h3>
						<p class="text-gray-600">三大平台分別負責算法模型、大模型訓練和隱私計算</p>
					</div>

					<!-- Business Application -->
					<div class="text-center">
						<div class="w-20 h-20 bg-purple-50 rounded-lg flex items-center justify-center mx-auto mb-6">
							<svg class="w-10 h-10 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
							</svg>
						</div>
						<h3 class="text-xl font-semibold mb-4 text-gray-900">商業應用</h3>
						<p class="text-gray-600">通過四大AI產品向B2C行業用戶提供具體服務</p>
					</div>
				</div>

				<div class="mt-16 text-center">
					<div class="inline-flex items-center space-x-6">
						<div class="flex items-center">
							<div class="w-4 h-4 bg-purple-600 rounded-full mr-2"></div>
							<span class="text-gray-700">基礎設施層</span>
						</div>
						<div class="w-12 h-px bg-gray-300"></div>
						<div class="flex items-center">
							<div class="w-4 h-4 bg-green-600 rounded-full mr-2"></div>
							<span class="text-gray-700">平台層</span>
						</div>
						<div class="w-12 h-px bg-gray-300"></div>
						<div class="flex items-center">
							<div class="w-4 h-4 bg-blue-600 rounded-full mr-2"></div>
							<span class="text-gray-700">應用層</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Core Advantages -->
	<section class="py-20 bg-white">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="text-center mb-16">
				<h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
					核心技術優勢
				</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					融合數據+算力+算法+工具的完整解決方案
				</p>
			</div>

			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
				<div class="text-center p-8 bg-gray-50 rounded-lg border border-gray-200">
					<div class="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"/>
						</svg>
					</div>
					<h3 class="text-xl font-semibold mb-3 text-gray-900">數據</h3>
					<p class="text-gray-600">覆蓋全國C端用戶數據網絡</p>
				</div>

				<div class="text-center p-8 bg-gray-50 rounded-lg border border-gray-200">
					<div class="w-16 h-16 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
						</svg>
					</div>
					<h3 class="text-xl font-semibold mb-3 text-gray-900">算力</h3>
					<p class="text-gray-600">分布式加密計算能力</p>
				</div>

				<div class="text-center p-8 bg-gray-50 rounded-lg border border-gray-200">
					<div class="w-16 h-16 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
						</svg>
					</div>
					<h3 class="text-xl font-semibold mb-3 text-gray-900">算法</h3>
					<p class="text-gray-600">AI+智能決策與生成算法</p>
				</div>

				<div class="text-center p-8 bg-gray-50 rounded-lg border border-gray-200">
					<div class="w-16 h-16 bg-orange-600 rounded-lg flex items-center justify-center mx-auto mb-6">
						<svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"/>
						</svg>
					</div>
					<h3 class="text-xl font-semibold mb-3 text-gray-900">工具</h3>
					<p class="text-gray-600">完整的AI應用工具鏈</p>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="py-20 bg-gray-900 text-white">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
			<h2 class="text-3xl md:text-4xl font-bold mb-6">
				體驗AI+智能決策技術
			</h2>
			<p class="text-xl mb-10 text-gray-300 max-w-2xl mx-auto">
				了解我們如何幫助您實現營銷運營ROI提升和業務數字化轉型
			</p>
			<div class="flex flex-col sm:flex-row gap-4 justify-center">
				<a href="/contact" class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold transition-colors">
					申請產品演示
				</a>
				<a href="/market" class="bg-transparent border-2 border-gray-300 text-gray-300 hover:bg-gray-300 hover:text-gray-900 px-8 py-4 rounded-lg font-semibold transition-colors">
					查看成功案例
				</a>
			</div>
		</div>
	</section>
</MainLayout> 