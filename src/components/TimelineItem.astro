---
export interface TimelineItemData {
	date: string;
	title: string;
	description: string;
}

export interface Props {
	year: string;
	items?: TimelineItemData[];
	layout?: "left" | "right";
	phase?: "blue" | "green" | "purple" | "orange";
}

const { year, items, layout = "left", phase = "blue" } = Astro.props;
---

<div class="grid grid-cols-1 md:grid-cols-11 gap-8 items-start">
	{layout === "left" ? (
		<>
			<div class="md:col-span-5">
				<div class="text-left">
					<div class={`timeline-date timeline-date-${phase} text-6xl md:text-8xl font-black p-4`}>
						{year}
					</div>
				</div>
			</div>
			<div class="md:col-span-6">
				<div class="bg-white p-8 rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow">
					<slot>
						{items && (
							<div class="space-y-6">
								{items.map((item) => (
									<div>
										<h3 class="text-2xl font-semibold text-gray-900 mb-2">{item.date} - {item.title}</h3>
										<p class="text-gray-600 text-lg">{item.description}</p>
									</div>
								))}
							</div>
						)}
					</slot>
				</div>
			</div>
		</>
	) : (
		<>
			<div class="md:col-span-6">
				<div class="bg-white p-8 rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow">
					<slot>
						{items && (
							<div class="space-y-6">
								{items.map((item) => (
									<div>
										<h3 class="text-2xl font-semibold text-gray-900 mb-2">{item.date} - {item.title}</h3>
										<p class="text-gray-600 text-lg">{item.description}</p>
									</div>
								))}
							</div>
						)}
					</slot>
				</div>
			</div>
			<div class="md:col-span-5 text-right">
				<div class={`timeline-date timeline-date-${phase} text-6xl md:text-8xl font-black p-4`}>
					{year}
				</div>
			</div>
		</>
	)}
</div>

<style>
	.timeline-date {
		font-family: 'Inter', sans-serif;
		letter-spacing: -0.05em;
		line-height: 1;
		font-weight: 900;
		background-size: 100% 100%;
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
		background-clip: text;
		display: inline-block;
	}

	/* Phase-specific gradients */
	.timeline-date-blue {
		background-image: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #1e40af 100%);
	}

	.timeline-date-green {
		background-image: linear-gradient(135deg, #4ade80 0%, #22c55e 50%, #16a34a 100%);
	}

	.timeline-date-purple {
		background-image: linear-gradient(135deg, #a855f7 0%, #8b5cf6 50%, #7c3aed 100%);
	}

	.timeline-date-orange {
		background-image: linear-gradient(135deg, #fb923c 0%, #f97316 50%, #ea580c 100%);
	}

	/* Fallback for browsers that don't support background-clip: text */
	@supports not (-webkit-background-clip: text) {
		.timeline-date-blue {
			color: #3b82f6;
		}

		.timeline-date-green {
			color: #22c55e;
		}

		.timeline-date-purple {
			color: #8b5cf6;
		}

		.timeline-date-orange {
			color: #f97316;
		}
	}

	@media (max-width: 768px) {
		.timeline-date {
			font-size: 4rem;
		}
	}
</style>