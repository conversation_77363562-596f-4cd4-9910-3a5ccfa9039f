---
import LanguageToggle from './LanguageToggle.astro';
---

<header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
		<div class="flex justify-between items-center h-16">
			<!-- Logo -->
			<div class="flex items-center">
				<a href="/" class="flex items-center space-x-2">
					<div class="w-8 h-8 bg-gradient-to-br from-blue-800 to-purple-300 rounded-lg flex items-center justify-center">
						<span class="text-white font-bold text-lg">👍🏻</span>
					</div>
					<span class="text-xl font-bold text-gray-900">青釭金融科技</span>
				</a>
			</div>

			<!-- Navigation -->
			<nav class="hidden md:flex space-x-8">
				<a href="/" class="text-gray-700 hover:text-gray-900 font-medium transition-colors">首頁</a>
				<div class="relative group">
					<a href="/market" class="text-gray-700 hover:text-gray-900 font-medium transition-colors">市場與案例</a>
					<div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
						<div class="py-1">
							<a href="/market#operations" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">市場運營狀況</a>
							<a href="/market#services" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">核心服務</a>
							<a href="/market#cases" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">成功案例</a>
							<a href="/market#analysis" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">市場分析</a>
						</div>
					</div>
				</div>
				<div class="relative group">
					<a href="/products" class="text-gray-700 hover:text-gray-900 font-medium transition-colors">產品與技術</a>
					<div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
						<div class="py-1">
							<a href="/products#fl-ai" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">FL.AI 隱私計算</a>
							<a href="/products#mxc-ai" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">MXC.AI 算法訓練</a>
							<a href="/products#rochat-ai" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Rochat.AI 大模型</a>
							<a href="/products#model-ai" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Model.AI 畫像模型</a>
							<a href="/products#strategy-ai" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Strategy.AI 智能決策</a>
							<a href="/products#robot-ai" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Robot.AI 機器人</a>
							<a href="/products#ar-digital" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">AR.Digital 虛擬人</a>
						</div>
					</div>
				</div>
				<div class="relative group">
					<a href="/about" class="text-gray-700 hover:text-gray-900 font-medium transition-colors">關於我們</a>
					<div class="absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
						<div class="py-1">
							<a href="/about#company" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">公司簡介</a>
							<a href="/about#team" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">核心團隊</a>
							<a href="/about#history" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">發展歷程</a>
						</div>
					</div>
				</div>
				<a href="/contact" class="text-gray-700 hover:text-gray-900 font-medium transition-colors">聯繫我們</a>
				<a href="/faq" class="text-gray-700 hover:text-gray-900 font-medium transition-colors">FAQ</a>
			</nav>

			<!-- Language Toggle & Mobile Menu -->
			<div class="flex items-center space-x-4">
				<LanguageToggle />
				<button class="md:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors" id="mobile-menu-button">
					<svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" id="menu-icon">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
					</svg>
					<svg class="w-6 h-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" id="close-icon">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
					</svg>
				</button>
			</div>
		</div>
	</div>

	<!-- Mobile Menu -->
	<div class="md:hidden hidden bg-white border-t border-gray-200" id="mobile-menu">
		<div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
			<a href="/" class="block px-3 py-2 text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-md">首頁</a>
			
			<!-- Market dropdown -->
			<div class="relative">
				<button class="w-full flex items-center justify-between px-3 py-2 text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-md focus:outline-none" onclick="toggleMobileDropdown('market-dropdown')">
					<span>市場與案例</span>
					<svg class="w-4 h-4 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
					</svg>
				</button>
				<div class="hidden pl-6 space-y-1" id="market-dropdown">
					<a href="/market#operations" class="block px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">市場運營狀況</a>
					<a href="/market#services" class="block px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">核心服務</a>
					<a href="/market#cases" class="block px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">成功案例</a>
					<a href="/market#analysis" class="block px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">市場分析</a>
				</div>
			</div>

			<!-- Products dropdown -->
			<div class="relative">
				<button class="w-full flex items-center justify-between px-3 py-2 text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-md focus:outline-none" onclick="toggleMobileDropdown('products-dropdown')">
					<span>產品與技術</span>
					<svg class="w-4 h-4 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
					</svg>
				</button>
				<div class="hidden pl-6 space-y-1" id="products-dropdown">
					<a href="/products#fl-ai" class="block px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">FL.AI 隱私計算</a>
					<a href="/products#mxc-ai" class="block px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">MXC.AI 算法訓練</a>
					<a href="/products#rochat-ai" class="block px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">Rochat.AI 大模型</a>
					<a href="/products#model-ai" class="block px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">Model.AI 畫像模型</a>
					<a href="/products#strategy-ai" class="block px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">Strategy.AI 智能決策</a>
					<a href="/products#robot-ai" class="block px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">Robot.AI 機器人</a>
					<a href="/products#ar-digital" class="block px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">AR.Digital 虛擬人</a>
				</div>
			</div>

			<!-- About dropdown -->
			<div class="relative">
				<button class="w-full flex items-center justify-between px-3 py-2 text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-md focus:outline-none" onclick="toggleMobileDropdown('about-dropdown')">
					<span>關於我們</span>
					<svg class="w-4 h-4 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
					</svg>
				</button>
				<div class="hidden pl-6 space-y-1" id="about-dropdown">
					<a href="/about#company" class="block px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">公司簡介</a>
					<a href="/about#team" class="block px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">核心團隊</a>
					<a href="/about#history" class="block px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 hover:text-gray-900 rounded-md">發展歷程</a>
				</div>
			</div>

			<a href="/contact" class="block px-3 py-2 text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-md">聯繫我們</a>
			<a href="/faq" class="block px-3 py-2 text-gray-700 hover:bg-gray-50 hover:text-gray-900 rounded-md">FAQ</a>
		</div>
	</div>
</header>

<script>
	// Mobile menu toggle
	document.addEventListener('DOMContentLoaded', function() {
		const mobileMenuButton = document.getElementById('mobile-menu-button');
		const mobileMenu = document.getElementById('mobile-menu');
		const menuIcon = document.getElementById('menu-icon');
		const closeIcon = document.getElementById('close-icon');
		
		if (mobileMenuButton && mobileMenu && menuIcon && closeIcon) {
			mobileMenuButton.addEventListener('click', function() {
				mobileMenu.classList.toggle('hidden');
				menuIcon.classList.toggle('hidden');
				closeIcon.classList.toggle('hidden');
			});
		}
	});

	// Mobile dropdown toggle function
	function toggleMobileDropdown(dropdownId: string) {
		const dropdown = document.getElementById(dropdownId);
		if (!dropdown) return;
		
		const button = dropdown.previousElementSibling as HTMLButtonElement;
		if (!button) return;
		
		const arrow = button.querySelector('svg');
		if (!arrow) return;
		
		if (dropdown.classList.contains('hidden')) {
			dropdown.classList.remove('hidden');
			arrow.style.transform = 'rotate(180deg)';
		} else {
			dropdown.classList.add('hidden');
			arrow.style.transform = 'rotate(0deg)';
		}
	}
</script>

<style>
	/* Custom styles for dropdown animations */
	.group:hover .group-hover\:opacity-100 {
		opacity: 1;
	}
	
	.group:hover .group-hover\:visible {
		visibility: visible;
	}
</style> 